# SecureAI ThreatScope

SecureAI ThreatScope is a cloud-native, AI-powered threat monitoring and analysis dashboard. It integrates Google Cloud services with Generative AI (Gemini API) to detect, explain, and visualize potential security anomalies in cloud infrastructure.

## 🚀 Features

- ✅ Real-time threat log analysis using Gemini API (Vertex AI)
- ✅ Streamlit-based frontend for interaction and visualization
- ✅ Google Cloud integration: Cloud Run, Firestore, IAM, Logging
- ✅ AI-based threat explanation and prioritization
- ✅ Startup script for automated VM provisioning

## 🛠 Tech Stack

- Python
- Streamlit
- Google Cloud Platform (GCP)
- Vertex AI (Gemini 2.5 Flash)
- Firestore
- Cloud Logging
- IAM & VPC

## 📁 Project Structure

```
SecureAI_ThreatScope/
│
├── app/                  # Streamlit frontend
│   └── main.py
│
├── scripts/              # GCP startup and setup scripts
│   └── startup.sh
│
├── utils/                # Utility functions (Gemini call, log parser)
│   ├── gemini_client.py
│   └── log_parser.py
│
├── README.md
└── requirements.txt
```

## 🔧 Setup Instructions

1. Clone the repository:
```bash
git clone https://github.com/yourusername/SecureAI_ThreatScope.git
cd SecureAI_ThreatScope
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the app:
```bash
streamlit run app/main.py
```

4. [Optional] Deploy to GCP using startup script or Dockerfile.

## 📌 Note

This is a student project under development as part of a GenAI + Cybersecurity initiative. Contributions welcome once the repo is public.

## 🧠 Author

Chaitanya Narasetti  
Email: <EMAIL>  
LinkedIn: [Narisetti Chaitanya Naidu](https://www.linkedin.com/in/narisetti-chaitanya-naidu)

---

MIT License
