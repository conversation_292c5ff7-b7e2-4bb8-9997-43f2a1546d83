import vertexai
from vertexai.preview.generative_models import GenerativeModel, Part

vertexai.init(project="freetrailproject-463104", location="us-central1")
model = GenerativeModel("gemini-1.5-flash-preview-0514")

def analyze_logs(log_text):
    prompt = f"You're a cybersecurity analyst. Analyze the following logs and explain any threats found.\n\nLogs:\n{log_text}"
    response = model.generate_content(prompt)
    return response.text