import streamlit as st
from genai_module import analyze_logs

st.set_page_config(page_title="SecureAI ThreatScope", layout="wide")
st.title("SecureAI ThreatScope 🔍")

log_input = st.text_area("Paste system logs here:")
if st.button("Analyze Logs"):
    if log_input.strip():
        with st.spinner("Analyzing..."):
            response = analyze_logs(log_input)
        st.subheader("🧠 Gemini Insight")
        st.write(response)
    else:
        st.warning("Please enter log data to analyze.")