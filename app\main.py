import streamlit as st
from utils.gemini_client import analyze_logs
from utils.log_parser import parse_logs

st.title("🔐 SecureAI ThreatScope")
uploaded_file = st.file_uploader("Upload a log file", type=["txt", "log"])

if uploaded_file:
    raw_logs = uploaded_file.read().decode("utf-8")
    parsed_logs = parse_logs(raw_logs)
    response = analyze_logs(parsed_logs)
    st.subheader("🧠 Gemini Analysis")
    st.write(response)
